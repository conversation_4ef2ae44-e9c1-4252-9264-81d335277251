package main

import (
	"os"
	"os/signal"
	"syscall"
	"time"

	"foxess.beech/config"
	"foxess.beech/forwarder"
	"foxess.beech/logger"
	"foxess.beech/microbus"
	"foxess.beech/mqtt"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.<PERSON>rror())
	}

	logger.Init(cfg)
	log := logger.GetLogger()

	microbusClient := microbus.NewClient(&cfg.MicroBus)

	// 创建MQTT客户端
	mqttClient, err := mqtt.NewClient(&cfg.MQTT)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create MQTT client")
	}

	// 创建消息桥接器
	messageBridge := forwarder.NewMessageBridge(microbusClient, mqttClient, &cfg.Hub)

	// 连接MQTT客户端
	if err := mqttClient.Connect(); err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to MQTT broker")
	}
	defer mqttClient.Disconnect()

	// 启动消息桥接服务
	if err := messageBridge.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start message bridge")
	}
	defer messageBridge.Stop()

	// Wait for interrupt signal to gracefully shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	log.Info().Msg("Shutdown signal received, starting graceful shutdown")

	// 断开客户端连接
	log.Info().Msg("Disconnecting clients")
	mqttClient.Disconnect()

	// 等待一小段时间让 goroutine 处理完成
	time.Sleep(2 * time.Second)
	log.Info().Msg("Graceful shutdown completed")

	log.Info().Msg("beech stopped")
}
