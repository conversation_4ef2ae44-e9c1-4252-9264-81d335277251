package main

import (
	"os"
	"os/signal"
	"syscall"
	"time"

	"foxess.beech/config"
	"foxess.beech/forwarder"
	"foxess.beech/logger"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.<PERSON>rror())
	}

	logger.Init(cfg)
	log := logger.GetLogger()

	// 创建消息桥接器
	messageBridge := forwarder.NewMessageBridge(cfg)

	// 启动消息桥接服务
	if err := messageBridge.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start message bridge")
	}
	defer messageBridge.Stop()

	// Wait for interrupt signal to gracefully shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signa
	<-sigChan
	log.Info().Msg("Shutdown signal received, starting graceful shutdown")

	// 等待一小段时间让 goroutine 处理完成
	time.Sleep(2 * time.Second)
	log.Info().Msg("Graceful shutdown completed")

	log.Info().Msg("beech stopped")
}
