package microbus

import (
	"foxess.beech/config"
	"foxess.beech/logger"

	fmicrobus "foxess.cloud/fmicrobus"
	"github.com/rs/zerolog"
)

// Client 封装microbus连接功能
type Client struct {
	micbus *fmicrobus.MicroBus
	config *config.MicroBusConfig
	logger zerolog.Logger
}

// NewClient 创建新的microbus客户端
func NewClient(cfg *config.MicroBusConfig) *Client {
	log := logger.GetLogger()

	micbus := fmicrobus.New(cfg.Brokers)

	client := &Client{
		micbus: micbus,
		config: cfg,
		logger: log,
	}

	return client
}

// GetMicroBus 返回底层的MicroBus实例，供forwarder直接使用
func (c *Client) GetMicroBus() *fmicrobus.MicroBus {
	return c.micbus
}

// GetConfig 返回配置信息
func (c *Client) GetConfig() *config.MicroBusConfig {
	return c.config
}

// GetLogger 返回日志记录器
func (c *Client) GetLogger() zerolog.Logger {
	return c.logger
}
