package forwarder

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/microbus"
	"foxess.beech/mqtt"
	"foxess.beech/types"

	fmicrobus "foxess.cloud/fmicrobus"
	"foxess.cloud/mop"
	"github.com/rs/zerolog"
)

const (
	Success              = 0     //成功
	ParametersParseError = 40256 //参数无法被正确解析

	SendMessageError  = 41204 //生成或发送报文出错
	RecvMessageError  = 40258 //收取消息发生异常
	ParseMessageError = 40259 //消息无法被正常解析
	ResultHasNoData   = 40302 //当前时段无数据
)

// MessageBridge 处理MicroBus和MQTT之间的消息桥接

type MessageBridge struct {
	microbusClient *microbus.Client
	mqttClient     *mqtt.Client
	hubConfig      *config.HubConfig
	logger         zerolog.Logger
	stopChan       chan struct{}
}

// HubSetResponse 表示hub设置响应
type HubSetResponse struct {
	Errno  int            `json:"errno"`
	Result map[string]any `json:"result"`
}

// HubGetRequest 表示hub获取请求
type HubGetRequest struct {
	Key            string `json:"key"`
	HasVersionHead int    `json:"hasVersionHead"`
}

// HubGetResponse 表示hub获取响应
type HubGetResponse struct {
	Errno  int       `json:"errno"`
	Result *HubValue `json:"result"`
}

// HubValue 表示从hub返回的值
type HubValue struct {
	SN        string         `json:"sn"`
	Version   string         `json:"version"`
	Key       string         `json:"key"`
	Values    map[string]any `json:"values"`
	RawValues string         `json:"rawValues"`
}

// RealtimeMessage 表示实时数据消息
type RealtimeMessage struct {
	Resource  string
	Payload   []byte
	Timestamp time.Time
}

// SettingRequestMessage 表示设置请求消息
type SettingRequestMessage struct {
	DevSN   string
	Request *types.SetRequest
}

// RealdataMessage 表示来自MicroBus的实时数据消息
type RealdataMessage struct {
	DevSN     string         `json:"devSN"`
	DataType  string         `json:"dataType"`
	Data      map[string]any `json:"data"`
	Timestamp int64          `json:"timestamp"`
}

// SettingMessage 表示来自MicroBus的设置消息
type SettingMessage struct {
	DevSN   string            `json:"devSN"`
	Values  map[string]string `json:"values"`
	Timeout int               `json:"timeout"` // 超时时间（秒），默认30秒
}

type Data struct {
	SN         string               `json:"sn"`         //设备SN
	ModuleSN   string               `json:"moduleSN"`   //模块sn
	Properties map[string]*Property `json:"properties"` //属性值
	Info       *Info                `json:"info"`       //设备信息
	ExInfo     *ExInfo              `json:"exInfo"`     //对外版本

}

// ExInfo 对外版本
type ExInfo struct {
	MasterVersion  string
	SlaveVersion   string
	ManagerVersion string
	AFCIVersion    string
}

// Info 新版本的设备属性信息
type Info struct {
	MasterVersion  string  `json:"masterVersion"`  //主CPU版本
	SlaveVersion   string  `json:"slaveVersion"`   //副CPU版本
	ManagerVersion string  `json:"managerVersion"` //HMI主CPU版本
	AFCIVersion    string  `json:"afciVersion"`    //AFCI版本
	DeviceFactory  uint16  `json:"deviceFactory"`  //厂商 0麦田温州 1麦田无锡
	ProductType    string  `json:"productType"`    //设备产品系列
	DeviceType     string  `json:"deviceType"`     //设备机型
	Capacity       float64 `json:"capacity"`       //设备容量
}

type Property struct {
	Value     any    `json:"value"`
	Timestamp int64  `json:"timestamp"`
	Unit      string `json:"unit"`
}

// NewMessageBridge 创建新的消息桥接器
func NewMessageBridge(microbusClient *microbus.Client, mqttClient *mqtt.Client, hubConfig *config.HubConfig) *MessageBridge {
	return &MessageBridge{
		microbusClient: microbusClient,
		mqttClient:     mqttClient,
		hubConfig:      hubConfig,
		logger:         logger.GetLogger(),
		stopChan:       make(chan struct{}),
	}
}

// Start 启动消息桥接服务
func (mb *MessageBridge) Start() error {
	// 直接使用底层MicroBus订阅实时数据
	micbus := mb.microbusClient.GetMicroBus()
	config := mb.microbusClient.GetConfig()

	// 订阅配置中的实时数据topics
	for _, pattern := range config.Topics {
		mb.logger.Info().Str("pattern", pattern).Msg("Subscribing to realtime data pattern")

		// 使用WaitAsync订阅实时数据
		micbus.WaitAsync(pattern, func(mmsg *fmicrobus.MicroMessage) {
			resource := mmsg.GetResource()
			payload := mmsg.GetPayload()
			timestampInt := mmsg.GetTimestamp()
			timestamp := time.Unix(timestampInt, 0)

			mb.logger.Debug().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Received realtime data from microbus")

			// 直接处理实时数据
			go mb.handleRealtimeMessage(resource, payload, timestamp)
		}, config.QueueSize)
	}

	// 直接使用底层MQTT订阅设置请求
	mb.mqttClient.Subscribe("foxess/device/:method/v0/:devSN/req", mb.mqttClient.GetConfig().QoS, mb.handleMQTTRequest)

	mb.logger.Info().Msg("Message bridge started successfully")
	return nil
}

// Stop 停止消息桥接服务
func (mb *MessageBridge) Stop() {
	mb.logger.Info().Msg("Stopping MessageBridge")
	close(mb.stopChan)
}

// handleRealtimeMessage 处理实时数据消息
func (mb *MessageBridge) handleRealtimeMessage(resource string, payload []byte, timestamp time.Time) {
	mb.logger.Debug().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Processing realtime data from MicroBus")

	// 解析resource路径: "$system/realdata/heatpump/{devSN}/type/{dataType}/json"
	ss := strings.Split(resource, "/")
	if len(ss) != 7 {
		mb.logger.Error().Str("resource", resource).Msg("Invalid resource format")
		return
	}

	// 验证是实时数据消息
	if ss[1] != "realdata" {
		mb.logger.Error().Str("resource", resource).Msg("Not a realtime data message")
		return
	}

	// 从resource路径中提取devSN和dataType
	// beech := ss[2]
	devSN := ss[3]
	dataType := ss[5]

	mb.logger.Info().Str("devSN", devSN).Str("dataType", dataType).Msg("Parsed realtime data message")

	if err := mb.forwardRealtimeDataToMQTT(devSN, dataType, payload, timestamp); err != nil {
		mb.logger.Error().Err(err).Str("resource", resource).Msg("Failed to process realtime message")
	}
}

// handleMQTTRequest 处理MQTT设置请求
func (mb *MessageBridge) handleMQTTRequest(ctx *mop.Context) {
	topic := ctx.Topic
	payload := ctx.Message.Payload()

	mb.logger.Debug().Str("topic", topic).Str("payload", string(payload)).Msg("Received setting request")

	// 解析设备 SN
	devSN := ctx.Param("devSN")
	if devSN == "" {
		mb.logger.Error().Str("topic", topic).Msg("Failed to extract device SN from topic")
		return
	}

	// 解析请求数据
	var req types.SetRequest
	if err := json.Unmarshal(payload, &req); err != nil {
		mb.logger.Error().Err(err).Str("topic", topic).Msg("Failed to unmarshal setting request")
		return
	}

	method := ctx.Param("method")
	mb.logger.Debug().Str("method", method).Str("devSN", devSN).Uint32("id", req.ID).Msg("Processing setting request")

	// 创建请求消息并异步处理
	reqMsg := &SettingRequestMessage{
		DevSN:   devSN,
		Request: &req,
	}

	go func() {
		if err := mb.processSettingRequest(reqMsg); err != nil {
			mb.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", req.ID).Msg("Failed to process setting request")
		}
	}()
}

// processSettingRequest 处理设置请求
func (mb *MessageBridge) processSettingRequest(reqMsg *SettingRequestMessage) error {
	mb.logger.Info().Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("base64_data", reqMsg.Request.Frame).Msg("Handling setting request from MQTT")

	// 解码 base64 frame 数据为十六进制字符串
	hexData, err := base64.StdEncoding.DecodeString(reqMsg.Request.Frame)
	if err != nil {
		mb.logger.Error().Err(err).Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("base64_data", reqMsg.Request.Frame).Msg("Failed to decode base64 frame data")

		// 返回解码错误响应
		resp := &types.SetResponse{
			ID:    reqMsg.Request.ID,
			Errno: 400, // 参数错误
			Frame: "",  // 失败时返回空数据
		}
		return mb.mqttClient.PublishSettingResponse(reqMsg.DevSN, resp)
	}

	hexString := string(hexData)
	mb.logger.Debug().Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("hex_data", hexString).Msg("Decoded base64 frame to hex string")

	// 调用Hub处理设置请求，传递十六进制数据
	errno, responseHexString, err := mb.SetDataWithHex(reqMsg.DevSN, hexString)
	if err != nil {
		mb.logger.Error().Err(err).Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Msg("Failed to call Hub for setting request")

		resp := &types.SetResponse{
			ID:    reqMsg.Request.ID,
			Errno: errno,
			Frame: "", // 失败时返回空数据
		}
		return mb.mqttClient.PublishSettingResponse(reqMsg.DevSN, resp)
	}

	// 将Hub响应的十六进制字符串编码为base64
	responseBase64 := base64.StdEncoding.EncodeToString([]byte(responseHexString))

	// 转换Hub响应为MQTT响应
	resp := &types.SetResponse{
		ID:    reqMsg.Request.ID,
		Errno: errno,
		Frame: responseBase64, // 返回base64编码的响应数据
	}

	mb.logger.Info().Str("devSN", reqMsg.DevSN).Uint32("id", reqMsg.Request.ID).Str("hex_response", responseHexString).Str("base64_response", responseBase64).Msg("Setting request processed")

	return mb.mqttClient.PublishSettingResponse(reqMsg.DevSN, resp)
}

// forwardRealtimeDataToMQTT 转发实时数据到MQTT
func (mb *MessageBridge) forwardRealtimeDataToMQTT(devSN, dataType string, payload []byte, timestamp time.Time) error {
	// 验证必需字段
	if devSN == "" {
		return fmt.Errorf("devSN is required for realdata message")
	}
	if dataType == "" {
		dataType = "json" // 默认数据类型
	}

	mb.logger.Info().Str("devSN", devSN).Str("dataType", dataType).Time("timestamp", timestamp).Msg("Forwarding realdata message to MQTT")

	var data Data
	e := json.Unmarshal(payload, &data)
	if e != nil {
		log.Println("parse payload error:", e)
		return e
	}

	m := make(map[string]any)
	for k, p := range data.Properties {
		m[k] = getPropertyValue(p)
	}

	payload, e = json.Marshal(m)
	if e != nil {
		log.Println("marshal payload error:", e)
		return e
	}

	topic := fmt.Sprintf("foxess/device/realData/v0/%s/json", devSN)
	if err := mb.mqttClient.Publish(topic, payload); err != nil {
		mb.logger.Error().Err(err).Str("devSN", devSN).Str("dataType", dataType).Str("topic", topic).Msg("Failed to publish realdata to MQTT")
		return fmt.Errorf("failed to publish realdata to MQTT: %w", err)
	}

	mb.logger.Debug().Str("devSN", devSN).Str("dataType", dataType).Msg("Realdata message forwarded successfully")

	return nil
}

// SetDataWithHex 向hub发送十六进制数据设置请求
func (mb *MessageBridge) SetDataWithHex(devSN, hexData string) (int, string, error) {
	mb.logger.Debug().Str("devSN", devSN).Str("hexData", hexData).Msg("Sending hex data request to hub")

	// 创建十六进制数据请求
	req := map[string]any{
		"sn":   devSN,
		"data": hexData,
	}

	reqData, err := json.Marshal(req)
	if err != nil {
		return SendMessageError, "", fmt.Errorf("failed to marshal request: %w", err)
	}

	resource := fmt.Sprintf(mb.hubConfig.SetResource, devSN)

	mb.logger.Debug().Str("resource", resource).Str("group", mb.hubConfig.Group).Msg("Sending hex data RequestGroupAll to hub")

	// 获取底层的MicroBus实例
	micbus := mb.microbusClient.GetMicroBus()
	msg := micbus.RequestGroupAll(mb.hubConfig.Group, resource, "", reqData, mb.hubConfig.Timeout)
	if msg == nil {
		return RecvMessageError, "", fmt.Errorf("failed to receive hub message: %w", err)
	}

	var result HubSetResponse
	if err := json.Unmarshal(msg.GetPayload(), &result); err != nil {
		return ParseMessageError, "", fmt.Errorf("failed to unmarshal hub result: %w", err)
	}
	if result.Errno != 0 {
		return result.Errno, "", fmt.Errorf("errno: %w", err)
	}

	var decodeData string
	di, ok := result.Result["data"]
	if !ok {
		return Success, "", nil
	}

	decodeData, ok = di.(string)
	if !ok {
		return ParametersParseError, "", fmt.Errorf("error: Coerce data to string fail")
	}
	data, err := base64.StdEncoding.DecodeString(decodeData)

	mb.logger.Debug().Str("devSN", devSN).Int("errno", result.Errno).Str("data", string(data)).Msg("Received hex data response from hub")

	return Success, string(data), nil
}

func getPropertyValue(value *Property) any {
	if value != nil {
		return value.Value
	}

	return nil
}
