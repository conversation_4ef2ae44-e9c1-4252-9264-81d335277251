package mqtt

import (
	"encoding/json"
	"fmt"
	"sync"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/types"

	"foxess.cloud/mop"
	"github.com/rs/zerolog"
)

// SettingRequestMessage 表示设置请求消息
type SettingRequestMessage struct {
	DevSN   string
	Request *types.SetRequest
}

// Client 表示MQTT客户端，基于 mop Engine
type Client struct {
	engine      *mop.Engine
	config      *config.MQTTConfig
	logger      zerolog.Logger
	connected   bool
	mu          sync.RWMutex
	setReqChan  chan *SettingRequestMessage
	readReqChan chan *SettingRequestMessage
	started     bool
}

// NewClient 创建新的MQTT客户端，使用 mop Engine
func NewClient(cfg *config.MQTTConfig) (*Client, error) {
	log := logger.GetLogger()

	// 创建 mop 配置
	mopConfig := &mop.Config{
		ClientID: cfg.ClientID,
		Broker:   cfg.Broker,
		Username: cfg.Username,
		Password: cfg.Password,
	}

	// 创建 mop Engine
	engine := mop.New(mopConfig)

	client := &Client{
		engine:      engine,
		config:      cfg,
		logger:      log,
		setReqChan:  make(chan *SettingRequestMessage, 100), // 缓冲通道
		readReqChan: make(chan *SettingRequestMessage, 100), // 缓冲通道
		started:     false,
		connected:   false,
	}

	log.Info().Str("broker", cfg.Broker).Str("client_id", cfg.ClientID).Msg("MQTT client created with mop engine")

	return client, nil
}

// Connect connects to the MQTT broker using mop Engine
func (c *Client) Connect() error {
	c.logger.Info().Msg("Connecting to MQTT broker using mop engine")

	c.engine.Subscribe("foxess/device/:method/v0/:devSN/req", c.config.QoS, c.handleRequest)

	// 启动 mop Engine
	c.engine.Run()

	c.mu.Lock()
	c.connected = true
	c.mu.Unlock()

	c.logger.Info().Msg("Connected to MQTT broker via mop engine")
	return nil
}

// handleRequest 处理设置请求
func (c *Client) handleRequest(ctx *mop.Context) {
	topic := ctx.Topic
	payload := ctx.Message.Payload()

	c.logger.Debug().Str("topic", topic).Str("payload", string(payload)).Msg("Received setting request")

	// 解析设备 SN
	devSN := ctx.Param("devSN")
	if devSN == "" {
		c.logger.Error().Str("topic", topic).Msg("Failed to extract device SN from topic")
		return
	}

	// 解析请求数据
	var req types.SetRequest
	if err := json.Unmarshal(payload, &req); err != nil {
		c.logger.Error().Err(err).Str("topic", topic).Msg("Failed to unmarshal setting request")
		return
	}

	method := ctx.Param("method")
	fmt.Println(method)

	// 发送到请求通道
	reqMsg := &SettingRequestMessage{
		DevSN:   devSN,
		Request: &req,
	}

	select {
	case c.setReqChan <- reqMsg:
		c.logger.Debug().Str("devSN", devSN).Uint32("id", req.ID).Msg("Setting request queued")
	default:
		c.logger.Error().Str("devSN", devSN).Uint32("id", req.ID).Msg("Setting request channel full, dropping request")
	}
}

// Disconnect disconnects from the MQTT broker
func (c *Client) Disconnect() {
	c.logger.Info().Msg("Disconnecting from MQTT broker")

	c.mu.Lock()
	c.connected = false
	c.mu.Unlock()

	// mop Engine 没有直接的 Disconnect 方法，这里只是标记为断开
	c.logger.Info().Msg("Disconnected from MQTT broker")
}

// IsConnected returns whether the client is connected
func (c *Client) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.connected
}

func (c *Client) Publish(topic string, payload []byte) error {
	c.mu.RLock()
	connected := c.connected
	c.mu.RUnlock()

	if !connected {
		return fmt.Errorf("MQTT client not connected")
	}

	c.logger.Debug().Str("topic", topic).Int("payload_size", len(payload)).Msg("Publishing MQTT message")

	c.engine.Publish(topic, c.config.QoS, false, payload)

	c.logger.Debug().Str("topic", topic).Msg("MQTT message published successfully")

	return nil
}

func (c *Client) GetSetRequestChannel() <-chan *SettingRequestMessage {
	return c.setReqChan
}

func (c *Client) GetReadRequestChannel() <-chan *SettingRequestMessage {
	return c.readReqChan
}

// PublishSettingResponse 发布设置响应
func (c *Client) PublishSettingResponse(devSN string, resp *types.SetResponse) error {
	// 构建响应topic: foxess/device/setting/v0/{devSN}/resp
	respTopic := fmt.Sprintf("foxess/device/setting/v0/%s/resp", devSN)

	respData, err := json.Marshal(resp)
	if err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to marshal setting response")
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	if err := c.Publish(respTopic, respData); err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to publish setting response")
		return fmt.Errorf("failed to publish response: %w", err)
	}

	c.logger.Info().Str("topic", respTopic).Str("devSN", devSN).Uint32("id", resp.ID).Str("data", resp.Frame).Msg("Published setting response")
	return nil
}

// Close 关闭客户端和请求通道
func (c *Client) Close() {
	if c.setReqChan != nil {
		close(c.setReqChan)
	}
}
