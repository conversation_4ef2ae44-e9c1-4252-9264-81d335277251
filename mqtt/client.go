package mqtt

import (
	"encoding/json"
	"fmt"
	"sync"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/types"

	"foxess.cloud/mop"
	"github.com/rs/zerolog"
)

// Client 表示MQTT客户端，基于 mop Engine
type Client struct {
	engine    *mop.Engine
	config    *config.MQTTConfig
	logger    zerolog.Logger
	connected bool
	mu        sync.RWMutex
}

// NewClient 创建新的MQTT客户端，使用 mop Engine
func NewClient(cfg *config.MQTTConfig) (*Client, error) {
	log := logger.GetLogger()

	// 创建 mop 配置
	mopConfig := &mop.Config{
		ClientID: cfg.ClientID,
		Broker:   cfg.Broker,
		Username: cfg.Username,
		Password: cfg.Password,
	}

	// 创建 mop Engine
	engine := mop.New(mopConfig)

	client := &Client{
		engine:    engine,
		config:    cfg,
		logger:    log,
		connected: false,
	}

	log.Info().Str("broker", cfg.Broker).Str("client_id", cfg.ClientID).Msg("MQTT client created with mop engine")

	return client, nil
}

// Connect connects to the MQTT broker using mop Engine
func (c *Client) Connect() error {
	c.logger.Info().Msg("Connecting to MQTT broker using mop engine")

	// 启动 mop Engine
	c.engine.Run()

	c.mu.Lock()
	c.connected = true
	c.mu.Unlock()

	c.logger.Info().Msg("Connected to MQTT broker via mop engine")
	return nil
}

// Disconnect disconnects from the MQTT broker
func (c *Client) Disconnect() {
	c.logger.Info().Msg("Disconnecting from MQTT broker")

	c.mu.Lock()
	c.connected = false
	c.mu.Unlock()

	// mop Engine 没有直接的 Disconnect 方法，这里只是标记为断开
	c.logger.Info().Msg("Disconnected from MQTT broker")
}

// IsConnected returns whether the client is connected
func (c *Client) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.connected
}

// Publish 发布消息到MQTT
func (c *Client) Publish(topic string, payload []byte) error {
	c.mu.RLock()
	connected := c.connected
	c.mu.RUnlock()

	if !connected {
		return fmt.Errorf("MQTT client not connected")
	}

	c.logger.Debug().Str("topic", topic).Int("payload_size", len(payload)).Msg("Publishing MQTT message")

	c.engine.Publish(topic, c.config.QoS, false, payload)

	c.logger.Debug().Str("topic", topic).Msg("MQTT message published successfully")

	return nil
}

// Subscribe 订阅MQTT主题
func (c *Client) Subscribe(topic string, qos byte, handler mop.HandlerFunc) {
	c.engine.Subscribe(topic, qos, handler)
}

// PublishSettingResponse 发布设置响应
func (c *Client) PublishSettingResponse(devSN string, resp *types.SetResponse) error {
	// 构建响应topic: foxess/device/setting/v0/{devSN}/resp
	respTopic := fmt.Sprintf("foxess/device/setting/v0/%s/resp", devSN)

	respData, err := json.Marshal(resp)
	if err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to marshal setting response")
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	if err := c.Publish(respTopic, respData); err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to publish setting response")
		return fmt.Errorf("failed to publish response: %w", err)
	}

	c.logger.Info().Str("topic", respTopic).Str("devSN", devSN).Uint32("id", resp.ID).Str("data", resp.Frame).Msg("Published setting response")
	return nil
}

// GetEngine 返回底层的mop Engine实例
func (c *Client) GetEngine() *mop.Engine {
	return c.engine
}

// GetConfig 返回配置信息
func (c *Client) GetConfig() *config.MQTTConfig {
	return c.config
}

// GetLogger 返回日志记录器
func (c *Client) GetLogger() zerolog.Logger {
	return c.logger
}
