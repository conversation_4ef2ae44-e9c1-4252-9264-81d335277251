package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"

	fmicrobus "foxess.cloud/fmicrobus"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// Property 表示设备属性的详细信息，匹配forwarder中的Property结构
type Property struct {
	Value     any    `json:"value"`
	Timestamp int64  `json:"timestamp"`
	Unit      string `json:"unit"`
}

// Data 表示实时数据的完整结构，匹配forwarder中的Data结构
type Data struct {
	SN         string               `json:"sn"`         // 设备SN
	ModuleSN   string               `json:"moduleSN"`   // 模块sn
	Properties map[string]*Property `json:"properties"` // 属性值
	Info       *Info                `json:"info"`       // 设备信息
	ExInfo     *ExInfo              `json:"exInfo"`     // 对外版本
}

// Info 设备属性信息
type Info struct {
	MasterVersion  string  `json:"masterVersion"`  // 主CPU版本
	SlaveVersion   string  `json:"slaveVersion"`   // 副CPU版本
	ManagerVersion string  `json:"managerVersion"` // HMI主CPU版本
	AFCIVersion    string  `json:"afciVersion"`    // AFCI版本
	DeviceFactory  uint16  `json:"deviceFactory"`  // 厂商 0麦田温州 1麦田无锡
	ProductType    string  `json:"productType"`    // 设备产品系列
	DeviceType     string  `json:"deviceType"`     // 设备机型
	Capacity       float64 `json:"capacity"`       // 设备容量
}

// ExInfo 对外版本
type ExInfo struct {
	MasterVersion  string `json:"masterVersion"`
	SlaveVersion   string `json:"slaveVersion"`
	ManagerVersion string `json:"managerVersion"`
	AFCIVersion    string `json:"afciVersion"`
}

// setRequest 表示设置请求的数据结构
type setRequest struct {
	ID    uint32 `json:"id"`    // 生成 uint32 类型唯一 id，用于确认请求和返回对应
	Frame string `json:"frame"` // base64 编码的数据字符串，原始数据为十六进制格式如 "010300000002C40B"
}

// setResponse 表示设置响应的数据结构
type setResponse struct {
	ID    uint32 `json:"id"`    // response 中的 id 与 request 中的 id 相同
	Errno int    `json:"errno"` // 错误码，0表示成功
	Frame string `json:"frame"` // base64 编码的响应数据字符串，原始数据为十六进制格式
}

func main() {
	if len(os.Args) < 2 {
		showUsage()
		return
	}

	command := os.Args[1]
	switch command {
	case "microbus":
		runMicroBusExample()
	case "mqtt":
		runMQTTExample()
	case "check":
		runPrerequisiteCheck()
	case "test":
		runCompleteFlowTest()
	case "multi":
		runMultiDeviceExample()
	case "modbus":
		runModbusExamples()
	default:
		showUsage()
	}
}

func showUsage() {
	fmt.Println("🚀 beech Examples")
	fmt.Println("===============")
	fmt.Println("Usage: go run main.go <command>")
	fmt.Println("")
	fmt.Println("Commands:")
	fmt.Println("  microbus  - Run MicroBus producer example")
	fmt.Println("  mqtt      - Run MQTT client example")
	fmt.Println("  check     - Check prerequisites")
	fmt.Println("  test      - Run complete flow test")
	fmt.Println("  multi     - Run multi-device MicroBus example")
	fmt.Println("  modbus    - Run Modbus command examples")
	fmt.Println("")
	fmt.Println("Prerequisites:")
	fmt.Println("  - Kafka running on localhost:9092")
	fmt.Println("  - MQTT broker running on localhost:1883")
	fmt.Println("  - beech service running: microbus_groups=beech LOG_LEVEL=debug ./beech")
}

func runMicroBusExample() {
	fmt.Println("📤 Running MicroBus Producer Example")
	fmt.Println("====================================")

	// 创建MicroBus客户端
	microbusClient := fmicrobus.New("localhost:9092")

	// 准备实时数据，使用与forwardRealtimeDataToMQTT匹配的Data结构
	realtimeData := createHeatPumpRealtimeData("device-001")

	realtimeBytes, err := json.Marshal(realtimeData)
	if err != nil {
		log.Fatal("Failed to marshal realtime data:", err)
	}

	// 发送实时数据
	realtimeResource := "$system/realdata/heatpump/device-001/type/devtype/json"
	microbusClient.StreamGroup("beech", realtimeResource, "device-001", realtimeBytes, 30)

	fmt.Printf("✅ Published realdata with resource: %s\n", realtimeResource)
	fmt.Printf("   Payload size: %d bytes\n", len(realtimeBytes))

	fmt.Println("\n📋 Message Flow:")
	fmt.Println("1. Real-time data: MicroBus.StreamGroup() → beech → MQTT (foxess/device/realData/v0/{devSN}/json)")
	fmt.Println("2. The payload will be processed by forwardRealtimeDataToMQTT and transformed to simple key-value pairs")
	fmt.Println("3. Check beech logs to see the message forwarding")

	time.Sleep(2 * time.Second)
	fmt.Println("✅ MicroBus example completed!")
}

// createHeatPumpRealtimeData 创建符合热泵设备规范的实时数据
func createHeatPumpRealtimeData(deviceSN string) *Data {
	currentTime := time.Now().Unix()

	return &Data{
		SN:       deviceSN,
		ModuleSN: "module-001",
		Properties: map[string]*Property{
			// 温度相关
			"waterInletTemp": {
				Value:     18.5,
				Timestamp: currentTime,
				Unit:      "°C",
			},
			"waterOutletTemp": {
				Value:     45.2,
				Timestamp: currentTime,
				Unit:      "°C",
			},
			"ambientTemp": {
				Value:     12.3,
				Timestamp: currentTime,
				Unit:      "°C",
			},
			"finTemp": {
				Value:     8.7,
				Timestamp: currentTime,
				Unit:      "°C",
			},
			"exhaustGasTemp": {
				Value:     65.4,
				Timestamp: currentTime,
				Unit:      "°C",
			},
			"suctionTemp": {
				Value:     5.2,
				Timestamp: currentTime,
				Unit:      "°C",
			},

			// 压力相关
			"lowPressureValue": {
				Value:     2.1,
				Timestamp: currentTime,
				Unit:      "MPa",
			},
			"highPressureValue": {
				Value:     1.8,
				Timestamp: currentTime,
				Unit:      "MPa",
			},

			// 电气参数
			"powerSupplyVoltageValue": {
				Value:     220.5,
				Timestamp: currentTime,
				Unit:      "V",
			},
			"acInputCurrent": {
				Value:     15.2,
				Timestamp: currentTime,
				Unit:      "A",
			},
			"busVolt": {
				Value:     380.1,
				Timestamp: currentTime,
				Unit:      "V",
			},

			// 工作状态
			"workMode": {
				Value:     "heating",
				Timestamp: currentTime,
				Unit:      "",
			},
			"unitWorkStatus": {
				Value:     "running",
				Timestamp: currentTime,
				Unit:      "",
			},
			"energyModel": {
				Value:     "eco",
				Timestamp: currentTime,
				Unit:      "",
			},

			// 能效数据
			"heatingRealTimeProduction": {
				Value:     8.5,
				Timestamp: currentTime,
				Unit:      "kW",
			},
			"heatingRealTimeConsumption": {
				Value:     2.1,
				Timestamp: currentTime,
				Unit:      "kW",
			},
			"heatingRealTimeCOPEER": {
				Value:     4.05,
				Timestamp: currentTime,
				Unit:      "",
			},

			// 开关状态
			"acFanEnable": {
				Value:     true,
				Timestamp: currentTime,
				Unit:      "",
			},
			"fourWayValve": {
				Value:     true,
				Timestamp: currentTime,
				Unit:      "",
			},
			"highPressSwitch": {
				Value:     false,
				Timestamp: currentTime,
				Unit:      "",
			},
			"lowPressSwitch": {
				Value:     false,
				Timestamp: currentTime,
				Unit:      "",
			},
		},
		Info: &Info{
			MasterVersion:  "v2.1.0",
			SlaveVersion:   "v2.0.5",
			ManagerVersion: "v1.8.2",
			AFCIVersion:    "v1.2.1",
			DeviceFactory:  0,
			ProductType:    "heatpump",
			DeviceType:     "HP-10KW",
			Capacity:       10.5,
		},
		ExInfo: &ExInfo{
			MasterVersion:  "v2.1.0",
			SlaveVersion:   "v2.0.5",
			ManagerVersion: "v1.8.2",
			AFCIVersion:    "v1.2.1",
		},
	}
}

func runMultiDeviceExample() {
	fmt.Println("📤 Running Multi-Device MicroBus Example")
	fmt.Println("=========================================")

	// 创建MicroBus客户端
	microbusClient := fmicrobus.New("localhost:9092")

	// 模拟多个设备发送数据
	devices := []string{"device-001", "device-002", "device-003"}

	for i, deviceSN := range devices {
		fmt.Printf("\n📡 Sending data for device %s...\n", deviceSN)

		// 为每个设备创建不同的数据
		realtimeData := createHeatPumpRealtimeData(deviceSN)

		// 稍微修改一些参数以区分设备
		if props := realtimeData.Properties; props != nil {
			if temp, ok := props["waterOutletTemp"]; ok {
				temp.Value = 45.2 + float64(i)*2.5 // 不同设备不同温度
			}
			if power, ok := props["heatingRealTimeProduction"]; ok {
				power.Value = 8.5 + float64(i)*1.2 // 不同设备不同功率
			}
		}

		realtimeBytes, err := json.Marshal(realtimeData)
		if err != nil {
			log.Printf("Failed to marshal realtime data for %s: %v", deviceSN, err)
			continue
		}

		// 发送实时数据
		realtimeResource := fmt.Sprintf("$system/realdata/heatpump/%s/type/devtype/json", deviceSN)
		microbusClient.StreamGroup("beech", realtimeResource, deviceSN, realtimeBytes, 30)

		fmt.Printf("✅ Published realdata for %s with resource: %s\n", deviceSN, realtimeResource)
		fmt.Printf("   Payload size: %d bytes\n", len(realtimeBytes))

		// 短暂延迟以避免同时发送
		time.Sleep(500 * time.Millisecond)
	}

	fmt.Println("\n📋 Multi-Device Message Flow:")
	fmt.Println("1. Multiple devices: MicroBus.StreamGroup() → beech → MQTT")
	fmt.Println("2. Each device data will be processed independently")
	fmt.Println("3. Check beech logs to see all device messages being forwarded")

	time.Sleep(2 * time.Second)
	fmt.Println("✅ Multi-device MicroBus example completed!")
}

func runMQTTExample() {
	fmt.Println("📤 Running MQTT Client Example")
	fmt.Println("===============================")

	// 配置MQTT客户端选项
	opts := mqtt.NewClientOptions()
	opts.AddBroker("tcp://localhost:1883")
	opts.SetClientID("beech-example-client")

	// 创建MQTT客户端并连接
	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		log.Fatal("Failed to connect to MQTT broker:", token.Error())
	}
	defer client.Disconnect(250)

	fmt.Println("✅ Connected to MQTT broker")

	// 订阅响应topics
	setupMQTTSubscriptions(client)

	// 等待订阅建立
	time.Sleep(1 * time.Second)

	// 发送设置请求
	sendsetRequest(client)

	// 等待响应
	time.Sleep(5 * time.Second)
	fmt.Println("✅ MQTT example completed!")
}

func setupMQTTSubscriptions(client mqtt.Client) {
	// 订阅设置响应topic
	respTopic := "foxess/device/set/v0/device-001/resp"
	token := client.Subscribe(respTopic, 0, func(client mqtt.Client, msg mqtt.Message) {
		fmt.Printf("📥 Received set response: %s\n", string(msg.Payload()))

		var resp setResponse
		if err := json.Unmarshal(msg.Payload(), &resp); err == nil {
			if resp.Errno == 0 && resp.Frame != "" {
				// 解码 base64 响应数据
				hexData, err := base64.StdEncoding.DecodeString(resp.Frame)
				if err != nil {
					fmt.Printf("   ⚠️  Failed to decode base64 response: %v\n", err)
				} else {
					fmt.Printf("   ✅ set request successful!\n")
					fmt.Printf("      Base64 response: %s\n", resp.Frame)
					fmt.Printf("      Hex response: %s\n", string(hexData))
				}
			} else {
				fmt.Printf("   ⚠️  set request failed with errno: %d, frame: %s\n", resp.Errno, resp.Frame)
			}
		}
	})
	if token.Wait() && token.Error() != nil {
		log.Fatal("Failed to subscribe to set response:", token.Error())
	}

	fmt.Println("✅ Subscribed to response topics")
}

func sendsetRequest(client mqtt.Client) {
	reqTopic := "foxess/device/set/v0/device-001/req"

	// 原始十六进制数据
	hexData := "010300000002C40B"
	// 编码为 base64
	base64Data := base64.StdEncoding.EncodeToString([]byte(hexData))

	req := setRequest{
		ID:    12345,
		Frame: base64Data, // base64 编码的数据
	}

	reqData, err := json.Marshal(req)
	if err != nil {
		log.Fatal("Failed to marshal set request:", err)
	}

	fmt.Printf("📤 Sending set request to: %s\n", reqTopic)
	fmt.Printf("   Original hex data: %s\n", hexData)
	fmt.Printf("   Base64 encoded: %s\n", base64Data)
	token := client.Publish(reqTopic, 0, false, reqData)
	if token.Wait() && token.Error() != nil {
		log.Fatal("Failed to publish set request:", token.Error())
	}
}

func runPrerequisiteCheck() {
	fmt.Println("🔍 beech Prerequisites Check")
	fmt.Println("===========================")

	allGood := true

	// 检查Kafka
	fmt.Print("Checking Kafka (port 9092)... ")
	if checkPort(9092) {
		fmt.Println("✅ Running")
	} else {
		fmt.Println("❌ Not running")
		fmt.Println("   Start Kafka: docker run -p 9092:9092 apache/kafka")
		allGood = false
	}

	// 检查MQTT
	fmt.Print("Checking MQTT broker (port 1883)... ")
	if checkPort(1883) {
		fmt.Println("✅ Running")
	} else {
		fmt.Println("❌ Not running")
		fmt.Println("   Start MQTT: docker run -p 1883:1883 eclipse-mosquitto")
		allGood = false
	}

	// 检查beech可执行文件
	fmt.Print("Checking beech executable... ")
	if fileExists("../beech") {
		fmt.Println("✅ Found")
	} else {
		fmt.Println("❌ Not found")
		fmt.Println("   Build beech: cd .. && go build -o beech .")
		allGood = false
	}

	// 检查beech进程
	fmt.Print("Checking beech process... ")
	if checkProcess("./beech") {
		fmt.Println("✅ Running")
	} else {
		fmt.Println("⚠️  Not running")
		fmt.Println("   Start beech: cd .. && microbus_groups=beech LOG_LEVEL=debug ./beech")
	}

	fmt.Println("")
	if allGood {
		fmt.Println("🎉 All prerequisites are ready!")
	} else {
		fmt.Println("⚠️  Some prerequisites need attention.")
	}
}

func runCompleteFlowTest() {
	fmt.Println("🚀 beech Complete Flow Test")
	fmt.Println("==========================")

	// 先检查前置条件
	fmt.Println("Step 1: Checking prerequisites...")
	runPrerequisiteCheck()

	fmt.Println("\nStep 2: Testing MicroBus → beech → MQTT flow...")
	runMicroBusExample()

	fmt.Println("\nStep 3: Testing Multi-Device MicroBus → beech → MQTT flow...")
	runMultiDeviceExample()

	fmt.Println("\nStep 4: Testing MQTT → beech → Hub → MQTT flow...")
	runMQTTExample()

	fmt.Println("\n📋 Test Summary:")
	fmt.Println("================")
	fmt.Println("✅ Real-time data: MicroBus → beech → MQTT")
	fmt.Println("✅ Multi-device data: Multiple devices → beech → MQTT")
	fmt.Println("✅ set requests: MQTT → beech → Hub → MQTT")

	fmt.Println("\n🔧 Architecture Verified:")
	fmt.Println("=========================")
	fmt.Println("┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐")
	fmt.Println("│   MicroBus      │───▶│      beech        │───▶│      MQTT       │")
	fmt.Println("│   Producer      │    │   Forwarder     │    │    Broker       │")
	fmt.Println("└─────────────────┘    └─────────────────┘    └─────────────────┘")
	fmt.Println("                              │")
	fmt.Println("                              ▼")
	fmt.Println("                       ┌─────────────────┐")
	fmt.Println("                       │      Hub        │")
	fmt.Println("                       │   (Simulated)   │")
	fmt.Println("                       └─────────────────┘")

	fmt.Println("\n🎉 beech Complete Flow Test Finished!")
}

func runModbusExamples() {
	fmt.Println("🔧 Running Modbus Command Examples")
	fmt.Println("===================================")

	// 配置MQTT客户端选项
	opts := mqtt.NewClientOptions()
	opts.AddBroker("tcp://localhost:1883")
	opts.SetClientID("beech-modbus-test-client")

	// 创建MQTT客户端并连接
	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		log.Fatal("Failed to connect to MQTT broker:", token.Error())
	}
	defer client.Disconnect(250)

	fmt.Println("✅ Connected to MQTT broker")

	deviceSN := "heatpump-modbus-test"
	respTopic := fmt.Sprintf("foxess/device/set/v0/%s/resp", deviceSN)

	// 订阅响应
	token := client.Subscribe(respTopic, 0, func(client mqtt.Client, msg mqtt.Message) {
		var resp setResponse
		if err := json.Unmarshal(msg.Payload(), &resp); err == nil {
			if resp.Errno == 0 && resp.Frame != "" {
				// 解码 base64 响应数据
				hexData, err := base64.StdEncoding.DecodeString(resp.Frame)
				if err != nil {
					fmt.Printf("📥 ⚠️  Response ID:%d - Failed to decode base64: %v\n", resp.ID, err)
				} else {
					hexString := string(hexData)
					fmt.Printf("📥 ✅ Response ID:%d Success\n", resp.ID)
					fmt.Printf("      Base64: %s\n", resp.Frame)
					fmt.Printf("      Hex: %s\n", hexString)
					parseModbusResponse(hexString)
				}
			} else {
				fmt.Printf("📥 ❌ Response ID:%d Failed, Errno: %d, Frame: %s\n", resp.ID, resp.Errno, resp.Frame)
			}
		}
	})
	if token.Wait() && token.Error() != nil {
		log.Fatal("Failed to subscribe to response topic:", token.Error())
	}

	fmt.Printf("✅ Subscribed to response topic: %s\n", respTopic)
	time.Sleep(1 * time.Second)

	// Modbus命令示例
	modbusCommands := []struct {
		name        string
		description string
		frame       string
	}{
		{
			name:        "Read Temperature",
			description: "读取温度寄存器（地址0x0000，数量2）",
			frame:       "010300000002C40B",
		},
		{
			name:        "Set Target Temperature",
			description: "设置目标温度为30度（地址0x0001，值0x001E）",
			frame:       "010600010001E59CA",
		},
		{
			name:        "Read Work Mode",
			description: "读取工作模式（地址0x0005，数量1）",
			frame:       "010300050001940A",
		},
		{
			name:        "Set Work Mode",
			description: "设置工作模式为制热（地址0x0005，值0x0001）",
			frame:       "01060005000119CB",
		},
		{
			name:        "Read Multiple Registers",
			description: "读取多个寄存器（地址0x0010，数量5）",
			frame:       "01030010000584CE",
		},
	}

	fmt.Println("\n🚀 Sending Modbus command examples...")

	for i, cmd := range modbusCommands {
		reqTopic := fmt.Sprintf("foxess/device/set/v0/%s/req", deviceSN)

		// 将十六进制数据编码为 base64
		base64Frame := base64.StdEncoding.EncodeToString([]byte(cmd.frame))

		req := setRequest{
			ID:    uint32(2000 + i + 1),
			Frame: base64Frame,
		}

		reqData, err := json.Marshal(req)
		if err != nil {
			log.Printf("Failed to marshal request for %s: %v", cmd.name, err)
			continue
		}

		fmt.Printf("\n📤 %d. %s\n", i+1, cmd.name)
		fmt.Printf("   Description: %s\n", cmd.description)
		fmt.Printf("   Hex Frame: %s\n", cmd.frame)
		fmt.Printf("   Base64 Frame: %s\n", base64Frame)
		fmt.Printf("   Request ID: %d\n", req.ID)

		token := client.Publish(reqTopic, 0, false, reqData)
		if token.Wait() && token.Error() != nil {
			log.Printf("Failed to publish request for %s: %v", cmd.name, token.Error())
		}

		// 等待响应
		time.Sleep(2 * time.Second)
	}

	fmt.Println("\n⏳ Waiting for final responses...")
	time.Sleep(3 * time.Second)

	fmt.Println("\n💡 Modbus Command Examples Completed!")
	fmt.Println("   - Demonstrated various Modbus function codes")
	fmt.Println("   - Showed read/write operations")
	fmt.Println("   - Check beech logs for detailed processing")
}

// parseModbusResponse 解析Modbus响应帧
func parseModbusResponse(frame string) {
	if len(frame) < 6 {
		fmt.Printf("   📋 Invalid frame length\n")
		return
	}

	deviceAddr := frame[0:2]
	funcCode := frame[2:4]

	fmt.Printf("   📋 Device Address: 0x%s, Function Code: 0x%s", deviceAddr, funcCode)

	switch funcCode {
	case "03":
		if len(frame) >= 8 {
			byteCount := frame[4:6]
			fmt.Printf(", Data Bytes: 0x%s", byteCount)
			if len(frame) > 8 {
				data := frame[6 : len(frame)-4] // 排除CRC
				fmt.Printf(", Data: %s", data)
			}
		}
	case "06":
		if len(frame) >= 12 {
			regAddr := frame[4:8]
			regValue := frame[8:12]
			fmt.Printf(", Register: 0x%s, Value: 0x%s", regAddr, regValue)
		}
	}
	fmt.Println()
}

// 辅助函数
func checkPort(port int) bool {
	cmd := exec.Command("ss", "-an")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	portStr := fmt.Sprintf(":%d", port)
	return strings.Contains(string(output), portStr) &&
		strings.Contains(string(output), "LISTEN")
}

func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

func checkProcess(processName string) bool {
	cmd := exec.Command("pgrep", "-f", processName)
	err := cmd.Run()
	return err == nil
}
